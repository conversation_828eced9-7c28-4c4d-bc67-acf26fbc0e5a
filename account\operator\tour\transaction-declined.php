<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">



<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-red-600 to-red-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-red-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-red-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-red-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-red-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Declined</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-times-circle text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Declined Bookings</h1>
                    <p class="text-red-100">Bookings that have been rejected</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $booking_status = "declined";

                $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {

                        if ($row['payment_status'] === 'voucher') {
                            $showBadge = '<span class="badge inline-flex items-center bg-purple-100 text-purple-800 border border-purple-300 rounded-full px-3 py-1 text-xs font-semibold">
                            <i class="fas fa-ticket-alt mr-1 text-purple-500"></i>
                            Voucher
                            </span>';
                        } else {
                            $showBadge = '';
                        }
                        ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>" data-status="declined">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                        <?= $showBadge; ?>
                                    </h3>
                                    <div
                                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Declined
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-hotel text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Date:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['check_in_date']); ?> -
                                        <?= htmlspecialchars($row['check_out_date']); ?>
                                    </span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Total Pax:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['total_adults'] + $row['total_children']); ?>
                                    </span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="grid grid-cols-2 gap-2 pt-4 border-t border-gray-100">
                                <button data-modal-target="view-details-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-details-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button bg-blue-600 hover:bg-blue-700 text-white">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                                <button data-modal-target="delete-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="delete-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="action-button bg-red-600 hover:bg-red-700 text-white">
                                    <i class="fas fa-trash mr-2"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                        <!-- View Details Modal -->
                        <div id="view-details-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-md max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-500 rounded-t-lg">
                                        <h3 class="text-xl font-semibold text-white">
                                            Declined Booking Details
                                        </h3>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-6 h-6 flex justify-center items-center transition-colors duration-200"
                                            data-modal-hide="view-details-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>
                                    <!-- Modal Body -->
                                    <div class="p-4 bg-white rounded-lg shadow space-y-4">
                                        <!-- Booking Reference -->
                                        <div class="bg-gray-50 p-3 rounded-lg">
                                            <p class="text-xs text-gray-500 uppercase">Reference Number</p>
                                            <p class="text-sm font-semibold text-gray-800">
                                                <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                            </p>
                                        </div>

                                        <!-- Decline Reason -->
                                        <div class="bg-red-50 p-3 rounded-lg border border-red-100">
                                            <p class="text-xs text-red-500 uppercase font-medium">Reason for Decline</p>
                                            <p class="text-sm text-red-700 mt-1">
                                                <?= !empty($row['decline_reason']) ? htmlspecialchars($row['decline_reason']) : 'No reason provided'; ?>
                                            </p>
                                        </div>

                                        <!-- Declined By -->
                                        <div class="bg-gray-50 p-3 rounded-lg">
                                            <p class="text-xs text-gray-500 uppercase">Declined By</p>
                                            <p class="text-sm font-semibold text-gray-800">
                                                <?= htmlspecialchars($row['declined_by'] ?? 'System'); ?>
                                            </p>
                                        </div>

                                        <!-- Declined Date -->
                                        <div class="bg-gray-50 p-3 rounded-lg">
                                            <p class="text-xs text-gray-500 uppercase">Declined On</p>
                                            <p class="text-sm font-semibold text-gray-800">
                                                <?= !empty($row['declined_date']) ? date('F d, Y h:i A', strtotime($row['declined_date'])) : 'N/A'; ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                        <a href="booking-edit.php?id=<?= $row['booking_id']; ?>"
                                            class="action-button bg-green-600 hover:bg-green-700 text-white me-2">
                                            <i class="fas fa-edit mr-1"></i> Edit Booking
                                        </a>
                                        <button data-modal-hide="view-details-modal-<?= $row['booking_id']; ?>" type="button"
                                            class="action-button bg-gray-200 hover:bg-gray-300 text-gray-800">
                                            Close
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-red-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-times-circle text-red-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Declined Bookings</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">When bookings are declined, they will appear here
                                    for review and management.</p>
                                <a href="home.php" class="action-button bg-blue-600 hover:bg-blue-700 text-white">
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center border-red-200 bg-red-50">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-red-100 p-4 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-red-600 text-sm mb-4"><?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="location.reload()" class="action-button btn-view">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <?php
    require '_footer.php';
    ?>